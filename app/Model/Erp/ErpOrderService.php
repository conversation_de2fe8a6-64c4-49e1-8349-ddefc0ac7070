<?php declare(strict_types = 1);

namespace App\Model\Erp;

use App\Model\Erp\Connector\OrderConnector;
use App\Model\Erp\Exception\ErpException;
use App\Model\Orm\Order\Delivery\PhysicalDeliveryInformation;
use App\Model\Orm\Order\Order;
use App\Model\Erp\Entity\ErpOrder;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Order\Sync\OrderSyncHistory;
use App\Model\Orm\Order\Sync\OrderSyncType;
use App\Model\Orm\Orm;
use Nextras\Dbal\Utils\DateTimeImmutable;
use stdClass;
use <PERSON>\Debugger;
use Tracy\ILogger;

final class ErpOrderService
{

	private array $message;

	public function __construct(
		private readonly Orm $orm,
		private readonly OrderConnector $orderConnector,
	)
	{
	}

	public function createOrder(Order $order, OrderSyncType $syncType = OrderSyncType::Unexpected): void
	{
		$order->syncStartedAt = new DateTimeImmutable();
		$this->orm->persistAndFlush($order);
		$history = new OrderSyncHistory();
		$history->createdAt = new DateTimeImmutable();
		$history->orderNumber = $order->orderNumber;
		$history->type = $syncType;

		try {
			$erpOrder = new ErpOrder();
			$erpOrder->setDataFromOrder($order);
			$result = $this->orderConnector->saveOrder($erpOrder);
			$order = $this->processResult($order, $result);

			$data = $erpOrder->getData();
			$history->data = (array) $data;

			$order->syncedAt = new DateTimeImmutable();

			$this->orm->persistAndFlush($order);

			$history->response = (array) $result;

		} catch (\Throwable $exception) {
			$history->response = ['exception' => $exception];
			Debugger::log($exception, ILogger::EXCEPTION);
		}

		$this->orm->persistAndFlush($history);
	}

	public function updateOrder(Order $order, OrderSyncType $syncType = OrderSyncType::OrderUpdate): void
	{
		$order->syncStartedAt = new DateTimeImmutable();
		$this->orm->persistAndFlush($order);
		$history = new OrderSyncHistory();
		$history->createdAt = new DateTimeImmutable();
		$history->orderNumber = $order->orderNumber;
		$history->type = $syncType;

		try {
			//if ($this->configService->isEnvProduction()) { TODO
				$erpOrder = new ErpOrder();
				$erpOrder->setDataFromOrder($order);

				$history->data = (array) $erpOrder->getData();

				$result = $this->orderConnector->updateOrder($erpOrder);
				$order = $this->processResult($order, $result);
			//}

			$order->syncedAt = new DateTimeImmutable();

			/* TODO MichalK
			 * if ($order->subscriptionOrder instanceof Model\SubscriptionOrder && $order->subscriptionOrder->novikoSubmittedAt === null) {
				$order->subscriptionOrder->novikoSubmittedAt = new DateTimeImmutable();
				$log = new Model\SubscriptionOrderLog();
				$log->message = 'Order exported as open to Noviko';
				$log->type = Model\SubscriptionOrderLog::TYPE_INFO;
				$order->subscriptionOrder->logs->add($log);
			}*/

			/** @var Order $order */
			$order = $this->orm->persistAndFlush($order);

			$history->response = (array) $result;

		} catch (\Throwable $exception) {
			$history->response = ['exception' => $exception];
			Debugger::log($exception, ILogger::EXCEPTION);
			$this->message = [$exception->getMessage(), 'error'];
		}

		$this->orm->persistAndFlush($history);
	}

	public function getOrder(Order $order): ?ErpOrder
	{
		$result = $this->orderConnector->getOrder($order->novikoIdObjednavkaPart);
		if ($result === null) {
			return null;
		}
		$erpOrder = new ErpOrder();
		$erpOrder->setDataFromResult($result);
		return $erpOrder;
	}

	public function syncOrder(Order $order): Bool {
		$erpOrder = $this->getOrder($order);
		$erpStatus = $erpOrder->getstatus();
		$syncDeliveryInfo = $erpStatus >= 20;
		$syncItemsInfo = $erpStatus >= 11 && $erpStatus <= 20;
		if ($syncDeliveryInfo) {
			if ($order->useDeliveryAddress()) {
				$order->delivery->information->name = $erpOrder->getAdJmeno() . ' ' . $erpOrder->getAdPrijmeni();
				$order->delivery->information->company = $erpOrder->getAdSpolecnost();
				$order->delivery->information->phoneNumber = $erpOrder->getKontTel();
				$order->delivery->information->email = $erpOrder->getKontMail();
				$order->delivery->information->street = $erpOrder->getAdUlice();
				$order->delivery->information->city = $erpOrder->getAdMesto();
				$order->delivery->information->zip = $erpOrder->getAdPsc();
				$order->delivery->information->country = $this->orm->state->getBy(['code' => $erpOrder->getAdZemeIsoCode()]);
			} else {
				$order->name = $erpOrder->getAdJmeno() . ' ' . $erpOrder->getAdPrijmeni();
				$order->companyName = $erpOrder->getAdSpolecnost();
				$order->phone = $erpOrder->getKontTel();
				$order->email = $erpOrder->getKontMail();
				$order->street = $erpOrder->getAdUlice();
				$order->city = $erpOrder->getAdMesto();
				$order->zip = $erpOrder->getAdPsc();
				$order->country = $this->orm->state->getBy(['code' => $erpOrder->getAdZemeIsoCode()]);
			}
			$order->note = $erpOrder->getPoznProSklad();
		}
		return true;
	}

	public function getOrderStatus(Order $order): ?int
	{
		$erpOrder = $this->getOrder($order);
		return $erpOrder->getstatus();
	}

	public function syncStatus(Order $order): bool
	{
		$erpOrder = $this->getOrder($order);

		if ($erpOrder === null) {
			$this->message = ['order_noviko_msg_unknown_order', 'error'];
			return false;
		} elseif ($order->novikoStatus === $erpOrder->getStatus()) {
			$this->message = ['order_noviko_msg_update_status_no_changes', 'info'];
			return true;
		} else {
			$this->changeStatus($order, $erpOrder);
			$this->message = ['order_noviko_msg_update_status_changed', 'ok'];
			return true;
		}
	}

	/**
	 * change status from ERP to eshop
	 *
	 * @param Order $order
	 * @param ErpOrder $erpOrder
	 * @return Order
	 */
	public function changeStatus(Order $order, ErpOrder $erpOrder): Order
	{
		$order->novikoStatus = $erpOrder->getStatus(); // always store the noviko status
		$order->syncedAt = new DateTimeImmutable();

		switch ($erpOrder->getStatus()) {
			case Order::ERP_ORDER_STATUS_STORNO:
				/*if ($order->hasCreditNote) { // pri uplnem stornu by obj nemala uz mit dobropis
					throw new LogicException(sprintf('Order ID %d already has credit note', $order->id));
				}

				$this->orderModel->storno($order);
				if ($order->subscriptionOrder instanceof Model\SubscriptionOrder) {
					$this->subscriptionModel->cancelSubscriptionOrder($order->subscriptionOrder, Model\SubscriptionOrder::CANCELLING_REASON_NOVIKO_CANCEL);
				}
				$data = $this->creditNoteModel->getDataForLastCreditNote($order, Model\Order::CANCEL_REASON_DEFAULT); // kdyby nahodou obj uz mela nejaky D
				$creditNote = $this->creditNoteModel->create($order, $data);*/
				/*if ($creditNote) { // gopay refundace
					$this->creditNoteModel->doPaymentRefund($order, $creditNote);
				}*/

				/*$this->orderMailService->orderStorno($order, $creditNote);*/

				break;
			case Order::ERP_ORDER_STATUS_FORWARDED_TO_DELIVERY:
				$order->state = OrderState::Dispatched;
				break;
			case Order::ERP_ORDER_STATUS_RETURNED_BY_CARRIER:
				$order = $this->orm->persistAndFlush($order); // must be persisted before sending an email
				//$this->orderMailService->statusChanged($order);

				break;
			case Order::ERP_ORDER_STATUS_DELIVERED:
				//TODO create invoice and send email
				break;
			default:
				// other statuses, just set the status and do nothing more
				$order = $this->orm->persistAndFlush($order);

				break;
		}
		return $order;
	}

	public function cancelOrder(Order $order): Order
	{
		$history = new OrderSyncHistory();
		$history->createdAt = new DateTimeImmutable();
		$history->orderNumber = $order->orderNumber;
		$history->type = OrderSyncType::OrderCancel;

		try {
			$history->data = [
				'novikoIdObjednavkaPart' => $order->novikoIdObjednavkaPart,
			];

			$result = $this->orderConnector->cancelOrder($order->novikoIdObjednavkaPart);

			$history->response = (array) $result;

			if (isset($result->status)) {

				switch ($result->status) {

					case 0: // OK
					case 6: // obj je v Noviku již stornovaná = stornujeme i v eshopu
						// klient: při stornu ve stavu 14 a 19 nám někdo musí dávat vědět, že stornoval
						// todo send mail
						// if ($result->status !== 6 && in_array($order->novikoStatus, [Order::ERP_ORDER_STATUS_DELAYING_COMPLETE, Order::ERP_ORDER_STATUS_DELAYING_DELIVERY])) {
							//$this->orderMailService->orderStornoNovikoWarehouse($order);
						//}

						$order->syncedAt = new DateTimeImmutable();
						$order->novikoStatus = Order::ERP_ORDER_STATUS_STORNO;

						/** @var Order $order */
						$order = $this->orm->persistAndFlush($order);

						$this->message = ['order_noviko_msg_storno_ok', 'ok'];
						break;

					default: // ostatní statusy znamenají, že stornovat v Adamovi z nějakého důvodu již nelze
						// puvodne:
						// Noviko sklad: storno email chceme odeslat vzdy 11 az 21
						// ale Adriana: Tá možnosť, že to zachytia, existuje do chvíle, kým nenaskočí status 21 - to je už konečný status, ktorý sa nemení a v tej chvíli z e-shopu posielame aj 2. informačný e-mail s faktúrou.

						if ($order->novikoStatus === Order::ERP_ORDER_STATUS_DELAYING_STOCK) { // klient: Storno nelze ve stavu 11 = error msg a
							$this->message = ['order_noviko_msg_storno_rejected', 'error'];
						} else { // ostatni stavy -> lze odeslat email storno zadost do skladu
							//todo send mail
							//$this->orderMailService->orderStornoNovikoWarehouse($order);
							$this->message = ['order_noviko_msg_storno_rejected_request_warehouse', 'error'];
						}

						break;
				}
			}
		} catch (\Throwable $exception) {
			$history->response = ['exception' => $exception];
			Debugger::log($exception, ILogger::EXCEPTION);
			$this->message = [$exception->getMessage(), 'error'];
		}

		$this->orm->persistAndFlush($history);

		return $order;
	}

	private function processResult(Order $order, ?stdClass $result = null): Order
	{
		$objednavkaOdb = $result->objednavkaOdb;

		if (empty($result) || !isset($objednavkaOdb->id)) {
			throw new ErpException('Erp result not ok');
		}

		if ($objednavkaOdb->idObjednavkaPart !== (int) $order->novikoIdObjednavkaPart) { //// @phpstan-ignore-line
			throw new ErpException('idObjednavkaPart doesnt match id in the WS result');
		}

		if (!$order->extId) {
			// first sync save identifiers and status
			$order->extId = empty($objednavkaOdb->id) ? null : (string) $objednavkaOdb->id;
			$order->novikoHdId = empty($objednavkaOdb->idHDObj) ? null : (int) $objednavkaOdb->idHDObj;
			$order->novikoStatus = isset($result->status) ? (int) $result->status : null;
		}

		return $order;
	}

	public function getLastMessage(): stdClass
	{
		$msg = new stdClass();
		$msg->text = $this->message[0];
		$msg->type = $this->message[1];

		return $msg;
	}

}
